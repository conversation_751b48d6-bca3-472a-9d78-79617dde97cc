import type { MaterialSmartDescription, PaginationParams, ZhiDaNeededMaterialDescription } from '../../../../services/api'

// 列表相关类型
export interface DescriptionListState {
  data: MaterialSmartDescription[]
  loading: boolean
  total: number
  currentPage: number
  pageSize: number
}

export interface DescriptionListActions {
  fetchData: (params?: Partial<PaginationParams>) => Promise<void>
  handlePageChange: (page: number, size?: number) => void
  setCurrentPage: (page: number) => void
  setPageSize: (size: number) => void
}

// 详情相关类型
export interface DescriptionDetailState {
  selectedRecord: MaterialSmartDescription | null
  detailVisible: boolean
}

export interface DescriptionDetailActions {
  showDetail: (record: MaterialSmartDescription) => Promise<void>
  closeDetail: () => void
}

// 历史相关类型
export interface DescriptionHistoryState {
  selectedRecord: MaterialSmartDescription | null
  historyVisible: boolean
  historyData: MaterialSmartDescription[]
  historyLoading: boolean
}

export interface DescriptionHistoryActions {
  showHistory: (record: MaterialSmartDescription) => void
  closeHistory: () => void
}

// 组件 Props 类型
export interface DescriptionListSectionProps {
  onViewDetail: (record: MaterialSmartDescription) => void
  onViewHistory: (record: MaterialSmartDescription) => void
}

export interface DescriptionDetailSectionRef {
  showDetail: (record: MaterialSmartDescription) => Promise<void>
}

export interface DescriptionHistorySectionRef {
  showHistory: (record: MaterialSmartDescription) => void
}

// 重新导出常用类型
export type { MaterialSmartDescription, PaginationParams, ZhiDaNeededMaterialDescription }
