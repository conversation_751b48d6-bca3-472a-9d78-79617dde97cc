import { Module } from '@nestjs/common'
import { DatabaseModule } from '../../database/database.module'
import { MaterialSmartDescriptionService } from './material-smart-description.service'
import { MaterialSmartDescriptionController } from './material-smart-description.controller'
import { ForwardMaterialPlatformModule } from '../forward/material-platform/material-platform.module'

@Module({
  imports: [DatabaseModule, ForwardMaterialPlatformModule],
  controllers: [MaterialSmartDescriptionController],
  providers: [MaterialSmartDescriptionService],
  exports: [MaterialSmartDescriptionService],
})
export class MaterialSmartDescriptionModule {}
