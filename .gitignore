# compiled output
/dist
/node_modules
/ports.json

# Logs
logs
*.log
npm-debug.log*
pnpm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
/projects

# OS
.DS_Store

# Tests
/coverage
/.nyc_output

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace
.history
.git

# IDE - VSCode
.vscode
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/extensions.json

node_modules
assets
!packages/client/assets
/packages/service/build
/packages/service/buildInfo.json
buildInfo.json
tsconfig.tsbuildinfo
.tmp
buildfe
yarn.lock
package-lock.json
pnpm-lock.yaml
*.zip
*.tgz

ai_project_temp
_rustup
